"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShellHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/FederatedComponent */ \"(pages-dir-browser)/./components/FederatedComponent.tsx\");\n\n\nfunction ShellHomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Shell App\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"This is the shell application.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    scope: \"authApp\",\n                    module: \"./Login\",\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Loading Auth...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 27\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 15\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    scope: \"notesApp\",\n                    module: \"./NotesEditor\",\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Loading Notes...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 21\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n_c = ShellHomePage;\nvar _c;\n$RefreshReg$(_c, \"ShellHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrRTtBQUVuRCxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7OzBCQUNDLDhEQUFDQzswQkFBRzs7Ozs7OzBCQUNKLDhEQUFDQzswQkFBRTs7Ozs7OzBCQUNILDhEQUFDRjswQkFDTyw0RUFBQ0Ysc0VBQWtCQTtvQkFDakJLLE9BQU07b0JBQ05DLFFBQU87b0JBQ1BDLHdCQUFVLDhEQUFDTDtrQ0FBSTs7Ozs7Ozs7Ozs7Ozs7OzswQkFHekIsOERBQUNBOzBCQUVDLDRFQUFDRixzRUFBa0JBO29CQUNqQkssT0FBTTtvQkFDTkMsUUFBTztvQkFDUEMsd0JBQVUsOERBQUNMO2tDQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pCO0tBdEJ3QkQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXEZ1bGwgU3RhY2sgRGV2IFByb2plY3RzXFxQb3N0cyBQcm9qZWN0XFxNRktCIFRhc2sgQm9hcmRcXEZyb250ZW5kIEtub3dsZWRnZSBCYXNlIEFwcFxcc2hlbGwtYXBwXFxwYWdlc1xcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBGZWRlcmF0ZWRDb21wb25lbnQgZnJvbSBcIi4uL2NvbXBvbmVudHMvRmVkZXJhdGVkQ29tcG9uZW50XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaGVsbEhvbWVQYWdlKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2PlxyXG4gICAgICA8aDE+U2hlbGwgQXBwPC9oMT5cclxuICAgICAgPHA+VGhpcyBpcyB0aGUgc2hlbGwgYXBwbGljYXRpb24uPC9wPlxyXG4gICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxGZWRlcmF0ZWRDb21wb25lbnRcclxuICAgICAgICAgICAgICAgIHNjb3BlPVwiYXV0aEFwcFwiXHJcbiAgICAgICAgICAgICAgICBtb2R1bGU9XCIuL0xvZ2luXCJcclxuICAgICAgICAgICAgICAgIGZhbGxiYWNrPXs8ZGl2PkxvYWRpbmcgQXV0aC4uLjwvZGl2Pn1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdj5cclxuICAgICAgICBcclxuICAgICAgICA8RmVkZXJhdGVkQ29tcG9uZW50XHJcbiAgICAgICAgICBzY29wZT1cIm5vdGVzQXBwXCJcclxuICAgICAgICAgIG1vZHVsZT1cIi4vTm90ZXNFZGl0b3JcIlxyXG4gICAgICAgICAgZmFsbGJhY2s9ezxkaXY+TG9hZGluZyBOb3Rlcy4uLjwvZGl2Pn1cclxuICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkZlZGVyYXRlZENvbXBvbmVudCIsIlNoZWxsSG9tZVBhZ2UiLCJkaXYiLCJoMSIsInAiLCJzY29wZSIsIm1vZHVsZSIsImZhbGxiYWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/index.tsx\n"));

/***/ })

});