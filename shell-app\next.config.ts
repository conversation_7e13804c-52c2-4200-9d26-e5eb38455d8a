import { NextConfig } from "next";
// import { Configuration } from "webpack";
//import { NextFederationPlugin } from "@module-federation/nextjs-mf";

const nextConfig: NextConfig = {
  // Temporarily disable module federation to test basic Next.js functionality
  // webpack: (
  //   config: Configuration,
  //   options: { isServer: boolean; dev: boolean }
  // ) => {
  //   const { isServer } = options;
  //   const federationConfig = {
  //     name: "shellApp",
  //     remotes: {
  //       authApp: `authApp@http://localhost:3001/_next/static/${
  //         isServer ? "ssr" : "chunks"
  //       }/remoteEntry.js`,
  //       notesApp: `notesApp@http://localhost:3002/_next/static/${
  //         isServer ? "ssr" : "chunks"
  //       }/remoteEntry.js`,
  //     },
  //     shared: {
  //       react: {
  //         singleton: true,
  //         eager: true,
  //         requiredVersion: false,
  //       },
  //       "react-dom": {
  //         singleton: true,
  //         eager: true,
  //         requiredVersion: false,
  //       },
  //       "react/jsx-runtime": {
  //         singleton: true,
  //         eager: true,
  //         requiredVersion: false,
  //       },
  //       "react/jsx-dev-runtime": {
  //         singleton: true,
  //         eager: true,
  //         requiredVersion: false,
  //       },
  //       "next/head": {
  //         singleton: true,
  //         eager: true,
  //         requiredVersion: false,
  //       },
  //     },
  //   };
  //   config.plugins?.push(
  //     new (require("@module-federation/nextjs-mf").NextFederationPlugin)({
  //       ...federationConfig,
  //       filename: "static/chunks/remoteEntry.js",
  //     })
  //   );
  //   return config;
  // },
};

export default nextConfig;
