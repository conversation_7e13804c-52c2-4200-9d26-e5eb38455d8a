import FederatedComponent from "../components/FederatedComponent";

export default function ShellHomePage() {
  return (
    <div>
      <h1>Shell App</h1>
      <p>This is the shell application.</p>
      <div>
            <FederatedComponent
            scope="authApp"
            module="./Login"
            fallback={<div>Loading Auth...</div>}
            />
      </div>
      <div>
        <h2>Notes App</h2>
        <FederatedComponent
          scope="notesApp"
          module="./NotesEditor"
          fallback={<div>Loading Notes...</div>}
        />
      </div>
    </div>
  );
}
