/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "(pages-dir-browser)/./components/FederatedComponent.tsx":
/*!*******************************************!*\
  !*** ./components/FederatedComponent.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst FederatedComponent = (param)=>{\n    let { scope, module, fallback = null } = param;\n    _s();\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FederatedComponent.useEffect\": ()=>{\n            const checkRemoteAvailability = {\n                \"FederatedComponent.useEffect.checkRemoteAvailability\": ()=>{\n                    if ( true && window[scope]) {\n                        setIsReady(true);\n                    } else {\n                        // Retry after a short delay\n                        setTimeout(checkRemoteAvailability, 100);\n                    }\n                }\n            }[\"FederatedComponent.useEffect.checkRemoteAvailability\"];\n            checkRemoteAvailability();\n        }\n    }[\"FederatedComponent.useEffect\"], [\n        scope\n    ]);\n    if (!isReady) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                \"Error loading \",\n                scope,\n                \"/\",\n                module,\n                \": \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n            lineNumber: 34,\n            columnNumber: 12\n        }, undefined);\n    }\n    const Component = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(// Dynamically load the remote module using scope and module props\n    ()=>window[scope].get(module).then((factory)=>{\n            try {\n                const Module = factory();\n                return Module;\n            } catch (err) {\n                console.error(\"Error loading module \".concat(scope, \"/\").concat(module, \":\"), err);\n                setError(err instanceof Error ? err.message : 'Unknown error');\n                throw err;\n            }\n        }).catch((err)=>{\n            console.error(\"Failed to load \".concat(scope, \"/\").concat(module, \":\"), err);\n            setError(err.message || 'Failed to load module');\n            // Return a fallback component\n            return ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        \"Failed to load \",\n                        scope,\n                        \"/\",\n                        module\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 22\n                }, undefined);\n        }), {\n        loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallback\n            }, void 0, false),\n        ssr: false // Disable SSR for federated components\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n        fallback: fallback,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FederatedComponent, \"nIdZnjqjlFc+pr9nRgRDhOQRjis=\");\n_c = FederatedComponent;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FederatedComponent);\nvar _c;\n$RefreshReg$(_c, \"FederatedComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/FederatedComponent.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cbiche%5CFull%20Stack%20Dev%20Projects%5CPosts%20Project%5CMFKB%20Task%20Board%5CFrontend%20Knowledge%20Base%20App%5Cshell-app%5Cpages%5Cindex.tsx&page=%2F!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cbiche%5CFull%20Stack%20Dev%20Projects%5CPosts%20Project%5CMFKB%20Task%20Board%5CFrontend%20Knowledge%20Base%20App%5Cshell-app%5Cpages%5Cindex.tsx&page=%2F! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./pages/index.tsx */ \"(pages-dir-browser)/./pages/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuNV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPUMlM0ElNUNVc2VycyU1Q2JpY2hlJTVDRnVsbCUyMFN0YWNrJTIwRGV2JTIwUHJvamVjdHMlNUNQb3N0cyUyMFByb2plY3QlNUNNRktCJTIwVGFzayUyMEJvYXJkJTVDRnJvbnRlbmQlMjBLbm93bGVkZ2UlMjBCYXNlJTIwQXBwJTVDc2hlbGwtYXBwJTVDcGFnZXMlNUNpbmRleC50c3gmcGFnZT0lMkYhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsZ0VBQW1CO0FBQzFDO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvaW5kZXgudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9cIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cbiche%5CFull%20Stack%20Dev%20Projects%5CPosts%20Project%5CMFKB%20Task%20Board%5CFrontend%20Knowledge%20Base%20App%5Cshell-app%5Cpages%5Cindex.tsx&page=%2F!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/dynamic.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/dynamic.js ***!
  \*********************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * This function lets you dynamically import a component.\n * It uses [React.lazy()](https://react.dev/reference/react/lazy) with [Suspense](https://react.dev/reference/react/Suspense) under the hood.\n *\n * Read more: [Next.js Docs: `next/dynamic`](https://nextjs.org/docs/app/building-your-application/optimizing/lazy-loading#nextdynamic)\n */ default: function() {\n        return dynamic;\n    },\n    noSSR: function() {\n        return noSSR;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _loadablesharedruntime = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./loadable.shared-runtime */ \"(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable.shared-runtime.js\"));\nconst isServerSide = \"object\" === 'undefined';\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    return {\n        default: (mod == null ? void 0 : mod.default) || mod\n    };\n}\nfunction noSSR(LoadableInitializer, loadableOptions) {\n    // Removing webpack and modules means react-loadable won't try preloading\n    delete loadableOptions.webpack;\n    delete loadableOptions.modules;\n    // This check is necessary to prevent react-loadable from initializing on the server\n    if (!isServerSide) {\n        return LoadableInitializer(loadableOptions);\n    }\n    const Loading = loadableOptions.loading;\n    // This will only be rendered on the server side\n    return ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            error: null,\n            isLoading: true,\n            pastDelay: false,\n            timedOut: false\n        });\n}\nfunction dynamic(dynamicOptions, options) {\n    let loadableFn = _loadablesharedruntime.default;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    // Support for direct import(), eg: dynamic(import('../hello-world'))\n    // Note that this is only kept for the edge case where someone is passing in a promise as first argument\n    // The react-loadable babel plugin will turn dynamic(import('../hello-world')) into dynamic(() => import('../hello-world'))\n    // To make sure we don't execute the import without rendering first\n    if (dynamicOptions instanceof Promise) {\n        loadableOptions.loader = ()=>dynamicOptions;\n    // Support for having import as a function, eg: dynamic(() => import('../hello-world'))\n    } else if (typeof dynamicOptions === 'function') {\n        loadableOptions.loader = dynamicOptions;\n    // Support for having first argument being options, eg: dynamic({loader: import('../hello-world')})\n    } else if (typeof dynamicOptions === 'object') {\n        loadableOptions = {\n            ...loadableOptions,\n            ...dynamicOptions\n        };\n    }\n    // Support for passing options, eg: dynamic(import('../hello-world'), {loading: () => <p>Loading something</p>})\n    loadableOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    const loaderFn = loadableOptions.loader;\n    const loader = ()=>loaderFn != null ? loaderFn().then(convertModule) : Promise.resolve(convertModule(()=>null));\n    // coming from build/babel/plugins/react-loadable-plugin.js\n    if (loadableOptions.loadableGenerated) {\n        loadableOptions = {\n            ...loadableOptions,\n            ...loadableOptions.loadableGenerated\n        };\n        delete loadableOptions.loadableGenerated;\n    }\n    // support for disabling server side rendering, eg: dynamic(() => import('../hello-world'), {ssr: false}).\n    if (typeof loadableOptions.ssr === 'boolean' && !loadableOptions.ssr) {\n        delete loadableOptions.webpack;\n        delete loadableOptions.modules;\n        return noSSR(loadableFn, loadableOptions);\n    }\n    return loadableFn({\n        ...loadableOptions,\n        loader: loader\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuNV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQXdFQTs7Ozs7Q0FLQyxHQUNELE9BcUVDO2VBckV1QkE7O0lBMUJSQyxLQUFLO2VBQUxBOzs7Ozs0RUFwREU7NEZBQ0c7QUFFckIsTUFBTUMsZUFBZSxPQUFPQyxNQUFXO0FBMkJ2Qyx5RkFBeUY7QUFDekYscUdBQXFHO0FBQ3JHLHFFQUFxRTtBQUNyRSxTQUFTQyxjQUFpQkMsR0FBZ0Q7SUFDeEUsT0FBTztRQUFFQyxTQUFTLENBQUNELE9BQUFBLE9BQUFBLEtBQUFBLElBQUFBLElBQTRCQyxPQUFBQSxLQUFXRDtJQUFJO0FBQ2hFO0FBaUJPLFNBQVNKLE1BQ2RNLG1CQUFrQyxFQUNsQ0MsZUFBa0M7SUFFbEMseUVBQXlFO0lBQ3pFLE9BQU9BLGdCQUFnQkMsT0FBTztJQUM5QixPQUFPRCxnQkFBZ0JFLE9BQU87SUFFOUIsb0ZBQW9GO0lBQ3BGLElBQUksQ0FBQ1IsY0FBYztRQUNqQixPQUFPSyxvQkFBb0JDO0lBQzdCO0lBRUEsTUFBTUcsVUFBVUgsZ0JBQWdCSSxPQUFPO0lBQ3ZDLGdEQUFnRDtJQUNoRCxPQUFPLGtCQUNMLHFCQUFDRCxTQUFBQTtZQUFRRSxPQUFPO1lBQU1DLFNBQVM7WUFBQ0MsV0FBVztZQUFPQyxVQUFVOztBQUVoRTtBQVFlLFNBQVNoQixRQUN0QmlCLGNBQTZDLEVBQzdDQyxPQUEyQjtJQUUzQixJQUFJQyxhQUFhQyx1QkFBQUEsT0FBUTtJQUV6QixJQUFJWixrQkFBc0M7UUFDeEMsd0RBQXdEO1FBQ3hESSxTQUFTO2dCQUFDLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7WUFDdkMsSUFBSSxDQUFDQSxXQUFXLE9BQU87WUFDdkIsSUFBSU0sSUFBb0IsRUFBbUI7Z0JBQ3pDLElBQUlQLFdBQVc7b0JBQ2IsT0FBTztnQkFDVDtnQkFDQSxJQUFJRCxPQUFPO29CQUNULE9BQ0UsV0FERixHQUNFLHNCQUFDVyxLQUFBQTs7NEJBQ0VYLE1BQU1ZLE9BQU87MENBQ2QscUJBQUNDLE1BQUFBLENBQUFBOzRCQUNBYixNQUFNYyxLQUFLOzs7Z0JBR2xCO1lBQ0Y7WUFDQSxPQUFPO1FBQ1Q7SUFDRjtJQUVBLHFFQUFxRTtJQUNyRSx3R0FBd0c7SUFDeEcsMkhBQTJIO0lBQzNILG1FQUFtRTtJQUNuRSxJQUFJViwwQkFBMEJXLFNBQVM7UUFDckNwQixnQkFBZ0JxQixNQUFNLEdBQUcsSUFBTVo7SUFDL0IsdUZBQXVGO0lBQ3pGLE9BQU8sSUFBSSxPQUFPQSxtQkFBbUIsWUFBWTtRQUMvQ1QsZ0JBQWdCcUIsTUFBTSxHQUFHWjtJQUN6QixtR0FBbUc7SUFDckcsT0FBTyxJQUFJLE9BQU9BLG1CQUFtQixVQUFVO1FBQzdDVCxrQkFBa0I7WUFBRSxHQUFHQSxlQUFlO1lBQUUsR0FBR1MsY0FBYztRQUFDO0lBQzVEO0lBRUEsZ0hBQWdIO0lBQ2hIVCxrQkFBa0I7UUFBRSxHQUFHQSxlQUFlO1FBQUUsR0FBR1UsT0FBTztJQUFDO0lBRW5ELE1BQU1ZLFdBQVd0QixnQkFBZ0JxQixNQUFNO0lBQ3ZDLE1BQU1BLFNBQVMsSUFDYkMsWUFBWSxPQUNSQSxXQUFXQyxJQUFJLENBQUMzQixpQkFDaEJ3QixRQUFRSSxPQUFPLENBQUM1QixjQUFjLElBQU07SUFFMUMsMkRBQTJEO0lBQzNELElBQUlJLGdCQUFnQnlCLGlCQUFpQixFQUFFO1FBQ3JDekIsa0JBQWtCO1lBQ2hCLEdBQUdBLGVBQWU7WUFDbEIsR0FBR0EsZ0JBQWdCeUIsaUJBQWlCO1FBQ3RDO1FBQ0EsT0FBT3pCLGdCQUFnQnlCLGlCQUFpQjtJQUMxQztJQUVBLDBHQUEwRztJQUMxRyxJQUFJLE9BQU96QixnQkFBZ0IwQixHQUFHLEtBQUssYUFBYSxDQUFDMUIsZ0JBQWdCMEIsR0FBRyxFQUFFO1FBQ3BFLE9BQU8xQixnQkFBZ0JDLE9BQU87UUFDOUIsT0FBT0QsZ0JBQWdCRSxPQUFPO1FBRTlCLE9BQU9ULE1BQU1rQixZQUFZWDtJQUMzQjtJQUVBLE9BQU9XLFdBQVc7UUFBRSxHQUFHWCxlQUFlO1FBQUVxQixRQUFRQTtJQUFvQjtBQUN0RSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcRnVsbCBTdGFjayBEZXYgUHJvamVjdHNcXFBvc3RzIFByb2plY3RcXHNyY1xcc2hhcmVkXFxsaWJcXGR5bmFtaWMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCBMb2FkYWJsZSBmcm9tICcuL2xvYWRhYmxlLnNoYXJlZC1ydW50aW1lJ1xuXG5jb25zdCBpc1NlcnZlclNpZGUgPSB0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJ1xuXG50eXBlIENvbXBvbmVudE1vZHVsZTxQID0ge30+ID0geyBkZWZhdWx0OiBSZWFjdC5Db21wb25lbnRUeXBlPFA+IH1cblxuZXhwb3J0IGRlY2xhcmUgdHlwZSBMb2FkZXJDb21wb25lbnQ8UCA9IHt9PiA9IFByb21pc2U8XG4gIFJlYWN0LkNvbXBvbmVudFR5cGU8UD4gfCBDb21wb25lbnRNb2R1bGU8UD5cbj5cblxuZXhwb3J0IGRlY2xhcmUgdHlwZSBMb2FkZXI8UCA9IHt9PiA9XG4gIHwgKCgpID0+IExvYWRlckNvbXBvbmVudDxQPilcbiAgfCBMb2FkZXJDb21wb25lbnQ8UD5cblxuZXhwb3J0IHR5cGUgTG9hZGVyTWFwID0geyBbbW9kdWxlOiBzdHJpbmddOiAoKSA9PiBMb2FkZXI8YW55PiB9XG5cbmV4cG9ydCB0eXBlIExvYWRhYmxlR2VuZXJhdGVkT3B0aW9ucyA9IHtcbiAgd2VicGFjaz8oKTogYW55XG4gIG1vZHVsZXM/KCk6IExvYWRlck1hcFxufVxuXG5leHBvcnQgdHlwZSBEeW5hbWljT3B0aW9uc0xvYWRpbmdQcm9wcyA9IHtcbiAgZXJyb3I/OiBFcnJvciB8IG51bGxcbiAgaXNMb2FkaW5nPzogYm9vbGVhblxuICBwYXN0RGVsYXk/OiBib29sZWFuXG4gIHJldHJ5PzogKCkgPT4gdm9pZFxuICB0aW1lZE91dD86IGJvb2xlYW5cbn1cblxuLy8gTm9ybWFsaXplIGxvYWRlciB0byByZXR1cm4gdGhlIG1vZHVsZSBhcyBmb3JtIHsgZGVmYXVsdDogQ29tcG9uZW50IH0gZm9yIGBSZWFjdC5sYXp5YC5cbi8vIEFsc28gZm9yIGJhY2t3YXJkIGNvbXBhdGlibGUgc2luY2UgbmV4dC9keW5hbWljIGFsbG93cyB0byByZXNvbHZlIGEgY29tcG9uZW50IGRpcmVjdGx5IHdpdGggbG9hZGVyXG4vLyBDbGllbnQgY29tcG9uZW50IHJlZmVyZW5jZSBwcm94eSBuZWVkIHRvIGJlIGNvbnZlcnRlZCB0byBhIG1vZHVsZS5cbmZ1bmN0aW9uIGNvbnZlcnRNb2R1bGU8UD4obW9kOiBSZWFjdC5Db21wb25lbnRUeXBlPFA+IHwgQ29tcG9uZW50TW9kdWxlPFA+KSB7XG4gIHJldHVybiB7IGRlZmF1bHQ6IChtb2QgYXMgQ29tcG9uZW50TW9kdWxlPFA+KT8uZGVmYXVsdCB8fCBtb2QgfVxufVxuXG5leHBvcnQgdHlwZSBEeW5hbWljT3B0aW9uczxQID0ge30+ID0gTG9hZGFibGVHZW5lcmF0ZWRPcHRpb25zICYge1xuICBsb2FkaW5nPzogKGxvYWRpbmdQcm9wczogRHluYW1pY09wdGlvbnNMb2FkaW5nUHJvcHMpID0+IFJlYWN0LlJlYWN0Tm9kZVxuICBsb2FkZXI/OiBMb2FkZXI8UD4gfCBMb2FkZXJNYXBcbiAgbG9hZGFibGVHZW5lcmF0ZWQ/OiBMb2FkYWJsZUdlbmVyYXRlZE9wdGlvbnNcbiAgc3NyPzogYm9vbGVhblxufVxuXG5leHBvcnQgdHlwZSBMb2FkYWJsZU9wdGlvbnM8UCA9IHt9PiA9IER5bmFtaWNPcHRpb25zPFA+XG5cbmV4cG9ydCB0eXBlIExvYWRhYmxlRm48UCA9IHt9PiA9IChcbiAgb3B0czogTG9hZGFibGVPcHRpb25zPFA+XG4pID0+IFJlYWN0LkNvbXBvbmVudFR5cGU8UD5cblxuZXhwb3J0IHR5cGUgTG9hZGFibGVDb21wb25lbnQ8UCA9IHt9PiA9IFJlYWN0LkNvbXBvbmVudFR5cGU8UD5cblxuZXhwb3J0IGZ1bmN0aW9uIG5vU1NSPFAgPSB7fT4oXG4gIExvYWRhYmxlSW5pdGlhbGl6ZXI6IExvYWRhYmxlRm48UD4sXG4gIGxvYWRhYmxlT3B0aW9uczogRHluYW1pY09wdGlvbnM8UD5cbik6IFJlYWN0LkNvbXBvbmVudFR5cGU8UD4ge1xuICAvLyBSZW1vdmluZyB3ZWJwYWNrIGFuZCBtb2R1bGVzIG1lYW5zIHJlYWN0LWxvYWRhYmxlIHdvbid0IHRyeSBwcmVsb2FkaW5nXG4gIGRlbGV0ZSBsb2FkYWJsZU9wdGlvbnMud2VicGFja1xuICBkZWxldGUgbG9hZGFibGVPcHRpb25zLm1vZHVsZXNcblxuICAvLyBUaGlzIGNoZWNrIGlzIG5lY2Vzc2FyeSB0byBwcmV2ZW50IHJlYWN0LWxvYWRhYmxlIGZyb20gaW5pdGlhbGl6aW5nIG9uIHRoZSBzZXJ2ZXJcbiAgaWYgKCFpc1NlcnZlclNpZGUpIHtcbiAgICByZXR1cm4gTG9hZGFibGVJbml0aWFsaXplcihsb2FkYWJsZU9wdGlvbnMpXG4gIH1cblxuICBjb25zdCBMb2FkaW5nID0gbG9hZGFibGVPcHRpb25zLmxvYWRpbmchXG4gIC8vIFRoaXMgd2lsbCBvbmx5IGJlIHJlbmRlcmVkIG9uIHRoZSBzZXJ2ZXIgc2lkZVxuICByZXR1cm4gKCkgPT4gKFxuICAgIDxMb2FkaW5nIGVycm9yPXtudWxsfSBpc0xvYWRpbmcgcGFzdERlbGF5PXtmYWxzZX0gdGltZWRPdXQ9e2ZhbHNlfSAvPlxuICApXG59XG5cbi8qKlxuICogVGhpcyBmdW5jdGlvbiBsZXRzIHlvdSBkeW5hbWljYWxseSBpbXBvcnQgYSBjb21wb25lbnQuXG4gKiBJdCB1c2VzIFtSZWFjdC5sYXp5KCldKGh0dHBzOi8vcmVhY3QuZGV2L3JlZmVyZW5jZS9yZWFjdC9sYXp5KSB3aXRoIFtTdXNwZW5zZV0oaHR0cHM6Ly9yZWFjdC5kZXYvcmVmZXJlbmNlL3JlYWN0L1N1c3BlbnNlKSB1bmRlciB0aGUgaG9vZC5cbiAqXG4gKiBSZWFkIG1vcmU6IFtOZXh0LmpzIERvY3M6IGBuZXh0L2R5bmFtaWNgXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYnVpbGRpbmcteW91ci1hcHBsaWNhdGlvbi9vcHRpbWl6aW5nL2xhenktbG9hZGluZyNuZXh0ZHluYW1pYylcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZHluYW1pYzxQID0ge30+KFxuICBkeW5hbWljT3B0aW9uczogRHluYW1pY09wdGlvbnM8UD4gfCBMb2FkZXI8UD4sXG4gIG9wdGlvbnM/OiBEeW5hbWljT3B0aW9uczxQPlxuKTogUmVhY3QuQ29tcG9uZW50VHlwZTxQPiB7XG4gIGxldCBsb2FkYWJsZUZuID0gTG9hZGFibGUgYXMgTG9hZGFibGVGbjxQPlxuXG4gIGxldCBsb2FkYWJsZU9wdGlvbnM6IExvYWRhYmxlT3B0aW9uczxQPiA9IHtcbiAgICAvLyBBIGxvYWRpbmcgY29tcG9uZW50IGlzIG5vdCByZXF1aXJlZCwgc28gd2UgZGVmYXVsdCBpdFxuICAgIGxvYWRpbmc6ICh7IGVycm9yLCBpc0xvYWRpbmcsIHBhc3REZWxheSB9KSA9PiB7XG4gICAgICBpZiAoIXBhc3REZWxheSkgcmV0dXJuIG51bGxcbiAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHtcbiAgICAgICAgICByZXR1cm4gbnVsbFxuICAgICAgICB9XG4gICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAge2Vycm9yLm1lc3NhZ2V9XG4gICAgICAgICAgICAgIDxiciAvPlxuICAgICAgICAgICAgICB7ZXJyb3Iuc3RhY2t9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gbnVsbFxuICAgIH0sXG4gIH1cblxuICAvLyBTdXBwb3J0IGZvciBkaXJlY3QgaW1wb3J0KCksIGVnOiBkeW5hbWljKGltcG9ydCgnLi4vaGVsbG8td29ybGQnKSlcbiAgLy8gTm90ZSB0aGF0IHRoaXMgaXMgb25seSBrZXB0IGZvciB0aGUgZWRnZSBjYXNlIHdoZXJlIHNvbWVvbmUgaXMgcGFzc2luZyBpbiBhIHByb21pc2UgYXMgZmlyc3QgYXJndW1lbnRcbiAgLy8gVGhlIHJlYWN0LWxvYWRhYmxlIGJhYmVsIHBsdWdpbiB3aWxsIHR1cm4gZHluYW1pYyhpbXBvcnQoJy4uL2hlbGxvLXdvcmxkJykpIGludG8gZHluYW1pYygoKSA9PiBpbXBvcnQoJy4uL2hlbGxvLXdvcmxkJykpXG4gIC8vIFRvIG1ha2Ugc3VyZSB3ZSBkb24ndCBleGVjdXRlIHRoZSBpbXBvcnQgd2l0aG91dCByZW5kZXJpbmcgZmlyc3RcbiAgaWYgKGR5bmFtaWNPcHRpb25zIGluc3RhbmNlb2YgUHJvbWlzZSkge1xuICAgIGxvYWRhYmxlT3B0aW9ucy5sb2FkZXIgPSAoKSA9PiBkeW5hbWljT3B0aW9uc1xuICAgIC8vIFN1cHBvcnQgZm9yIGhhdmluZyBpbXBvcnQgYXMgYSBmdW5jdGlvbiwgZWc6IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCcuLi9oZWxsby13b3JsZCcpKVxuICB9IGVsc2UgaWYgKHR5cGVvZiBkeW5hbWljT3B0aW9ucyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIGxvYWRhYmxlT3B0aW9ucy5sb2FkZXIgPSBkeW5hbWljT3B0aW9uc1xuICAgIC8vIFN1cHBvcnQgZm9yIGhhdmluZyBmaXJzdCBhcmd1bWVudCBiZWluZyBvcHRpb25zLCBlZzogZHluYW1pYyh7bG9hZGVyOiBpbXBvcnQoJy4uL2hlbGxvLXdvcmxkJyl9KVxuICB9IGVsc2UgaWYgKHR5cGVvZiBkeW5hbWljT3B0aW9ucyA9PT0gJ29iamVjdCcpIHtcbiAgICBsb2FkYWJsZU9wdGlvbnMgPSB7IC4uLmxvYWRhYmxlT3B0aW9ucywgLi4uZHluYW1pY09wdGlvbnMgfVxuICB9XG5cbiAgLy8gU3VwcG9ydCBmb3IgcGFzc2luZyBvcHRpb25zLCBlZzogZHluYW1pYyhpbXBvcnQoJy4uL2hlbGxvLXdvcmxkJyksIHtsb2FkaW5nOiAoKSA9PiA8cD5Mb2FkaW5nIHNvbWV0aGluZzwvcD59KVxuICBsb2FkYWJsZU9wdGlvbnMgPSB7IC4uLmxvYWRhYmxlT3B0aW9ucywgLi4ub3B0aW9ucyB9XG5cbiAgY29uc3QgbG9hZGVyRm4gPSBsb2FkYWJsZU9wdGlvbnMubG9hZGVyIGFzICgpID0+IExvYWRlckNvbXBvbmVudDxQPlxuICBjb25zdCBsb2FkZXIgPSAoKSA9PlxuICAgIGxvYWRlckZuICE9IG51bGxcbiAgICAgID8gbG9hZGVyRm4oKS50aGVuKGNvbnZlcnRNb2R1bGUpXG4gICAgICA6IFByb21pc2UucmVzb2x2ZShjb252ZXJ0TW9kdWxlKCgpID0+IG51bGwpKVxuXG4gIC8vIGNvbWluZyBmcm9tIGJ1aWxkL2JhYmVsL3BsdWdpbnMvcmVhY3QtbG9hZGFibGUtcGx1Z2luLmpzXG4gIGlmIChsb2FkYWJsZU9wdGlvbnMubG9hZGFibGVHZW5lcmF0ZWQpIHtcbiAgICBsb2FkYWJsZU9wdGlvbnMgPSB7XG4gICAgICAuLi5sb2FkYWJsZU9wdGlvbnMsXG4gICAgICAuLi5sb2FkYWJsZU9wdGlvbnMubG9hZGFibGVHZW5lcmF0ZWQsXG4gICAgfVxuICAgIGRlbGV0ZSBsb2FkYWJsZU9wdGlvbnMubG9hZGFibGVHZW5lcmF0ZWRcbiAgfVxuXG4gIC8vIHN1cHBvcnQgZm9yIGRpc2FibGluZyBzZXJ2ZXIgc2lkZSByZW5kZXJpbmcsIGVnOiBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vaGVsbG8td29ybGQnKSwge3NzcjogZmFsc2V9KS5cbiAgaWYgKHR5cGVvZiBsb2FkYWJsZU9wdGlvbnMuc3NyID09PSAnYm9vbGVhbicgJiYgIWxvYWRhYmxlT3B0aW9ucy5zc3IpIHtcbiAgICBkZWxldGUgbG9hZGFibGVPcHRpb25zLndlYnBhY2tcbiAgICBkZWxldGUgbG9hZGFibGVPcHRpb25zLm1vZHVsZXNcblxuICAgIHJldHVybiBub1NTUihsb2FkYWJsZUZuLCBsb2FkYWJsZU9wdGlvbnMpXG4gIH1cblxuICByZXR1cm4gbG9hZGFibGVGbih7IC4uLmxvYWRhYmxlT3B0aW9ucywgbG9hZGVyOiBsb2FkZXIgYXMgTG9hZGVyPFA+IH0pXG59XG4iXSwibmFtZXMiOlsiZHluYW1pYyIsIm5vU1NSIiwiaXNTZXJ2ZXJTaWRlIiwid2luZG93IiwiY29udmVydE1vZHVsZSIsIm1vZCIsImRlZmF1bHQiLCJMb2FkYWJsZUluaXRpYWxpemVyIiwibG9hZGFibGVPcHRpb25zIiwid2VicGFjayIsIm1vZHVsZXMiLCJMb2FkaW5nIiwibG9hZGluZyIsImVycm9yIiwiaXNMb2FkaW5nIiwicGFzdERlbGF5IiwidGltZWRPdXQiLCJkeW5hbWljT3B0aW9ucyIsIm9wdGlvbnMiLCJsb2FkYWJsZUZuIiwiTG9hZGFibGUiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJwIiwibWVzc2FnZSIsImJyIiwic3RhY2siLCJQcm9taXNlIiwibG9hZGVyIiwibG9hZGVyRm4iLCJ0aGVuIiwicmVzb2x2ZSIsImxvYWRhYmxlR2VuZXJhdGVkIiwic3NyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/dynamic.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"LoadableContext\", ({\n    enumerable: true,\n    get: function() {\n        return LoadableContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst LoadableContext = _react.default.createContext(null);\nif (true) {\n    LoadableContext.displayName = 'LoadableContext';\n} //# sourceMappingURL=loadable-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuNV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sb2FkYWJsZS1jb250ZXh0LnNoYXJlZC1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBTWFBOzs7ZUFBQUE7Ozs7NEVBSks7QUFJWCxNQUFNQSxrQkFBa0JDLE9BQUFBLE9BQUssQ0FBQ0MsYUFBYSxDQUFtQjtBQUVyRSxJQUFJQyxJQUFvQixFQUFtQjtJQUN6Q0gsZ0JBQWdCTSxXQUFXLEdBQUc7QUFDaEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXEZ1bGwgU3RhY2sgRGV2IFByb2plY3RzXFxQb3N0cyBQcm9qZWN0XFxzcmNcXHNoYXJlZFxcbGliXFxsb2FkYWJsZS1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbnR5cGUgQ2FwdHVyZUZuID0gKG1vZHVsZU5hbWU6IHN0cmluZykgPT4gdm9pZFxuXG5leHBvcnQgY29uc3QgTG9hZGFibGVDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dDxDYXB0dXJlRm4gfCBudWxsPihudWxsKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBMb2FkYWJsZUNvbnRleHQuZGlzcGxheU5hbWUgPSAnTG9hZGFibGVDb250ZXh0J1xufVxuIl0sIm5hbWVzIjpbIkxvYWRhYmxlQ29udGV4dCIsIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable.shared-runtime.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable.shared-runtime.js ***!
  \*************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// TODO: Remove use of `any` type.\n/**\n@copyright (c) 2017-present James Kyle <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE\n*/ // https://github.com/jamiebuilds/react-loadable/blob/v5.5.0/src/index.js\n// Modified to be compatible with webpack 4 / Next.js\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _loadablecontextsharedruntime = __webpack_require__(/*! ./loadable-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\");\nfunction resolve(obj) {\n    return obj && obj.default ? obj.default : obj;\n}\nconst ALL_INITIALIZERS = [];\nconst READY_INITIALIZERS = [];\nlet initialized = false;\nfunction load(loader) {\n    let promise = loader();\n    let state = {\n        loading: true,\n        loaded: null,\n        error: null\n    };\n    state.promise = promise.then((loaded)=>{\n        state.loading = false;\n        state.loaded = loaded;\n        return loaded;\n    }).catch((err)=>{\n        state.loading = false;\n        state.error = err;\n        throw err;\n    });\n    return state;\n}\nfunction createLoadableComponent(loadFn, options) {\n    var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n    let opts = Object.assign({\n        loader: null,\n        loading: null,\n        delay: 200,\n        timeout: null,\n        webpack: null,\n        modules: null\n    }, options);\n    /** @type LoadableSubscription */ let subscription = null;\n    function init() {\n        if (!subscription) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            const sub = new LoadableSubscription(loadFn, opts);\n            subscription = {\n                getCurrentValue: sub.getCurrentValue.bind(sub),\n                subscribe: sub.subscribe.bind(sub),\n                retry: sub.retry.bind(sub),\n                promise: sub.promise.bind(sub)\n            };\n        }\n        return subscription.promise();\n    }\n    // Server only\n    if (false) // removed by dead control flow\n{}\n    // Client only\n    if (!initialized && \"object\" !== 'undefined') {\n        // require.resolveWeak check is needed for environments that don't have it available like Jest\n        const moduleIds = opts.webpack && \"function\" === 'function' ? opts.webpack() : opts.modules;\n        if (moduleIds) {\n            READY_INITIALIZERS.push((ids)=>{\n                for (const moduleId of moduleIds){\n                    if (ids.includes(moduleId)) {\n                        return init();\n                    }\n                }\n            });\n        }\n    }\n    function useLoadableModule() {\n        _s();\n        init();\n        const context = _react.default.useContext(_loadablecontextsharedruntime.LoadableContext);\n        if (context && Array.isArray(opts.modules)) {\n            opts.modules.forEach((moduleName)=>{\n                context(moduleName);\n            });\n        }\n    }\n    _s(useLoadableModule, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n    function LoadableComponent(props, ref) {\n        _s1();\n        useLoadableModule();\n        const state = _react.default.useSyncExternalStore(subscription.subscribe, subscription.getCurrentValue, subscription.getCurrentValue);\n        _react.default.useImperativeHandle(ref, {\n            \"createLoadableComponent.LoadableComponent.useImperativeHandle\": ()=>({\n                    retry: subscription.retry\n                })\n        }[\"createLoadableComponent.LoadableComponent.useImperativeHandle\"], []);\n        return _react.default.useMemo({\n            \"createLoadableComponent.LoadableComponent.useMemo\": ()=>{\n                if (state.loading || state.error) {\n                    return /*#__PURE__*/ _react.default.createElement(opts.loading, {\n                        isLoading: state.loading,\n                        pastDelay: state.pastDelay,\n                        timedOut: state.timedOut,\n                        error: state.error,\n                        retry: subscription.retry\n                    });\n                } else if (state.loaded) {\n                    return /*#__PURE__*/ _react.default.createElement(resolve(state.loaded), props);\n                } else {\n                    return null;\n                }\n            }\n        }[\"createLoadableComponent.LoadableComponent.useMemo\"], [\n            props,\n            state\n        ]);\n    }\n    _s1(LoadableComponent, \"FetqI339RA+IfltT8VNzX8RMZ2Q=\", false, function() {\n        return [\n            useLoadableModule\n        ];\n    });\n    LoadableComponent.preload = ()=>init();\n    LoadableComponent.displayName = 'LoadableComponent';\n    return /*#__PURE__*/ _react.default.forwardRef(LoadableComponent);\n}\nclass LoadableSubscription {\n    promise() {\n        return this._res.promise;\n    }\n    retry() {\n        this._clearTimeouts();\n        this._res = this._loadFn(this._opts.loader);\n        this._state = {\n            pastDelay: false,\n            timedOut: false\n        };\n        const { _res: res, _opts: opts } = this;\n        if (res.loading) {\n            if (typeof opts.delay === 'number') {\n                if (opts.delay === 0) {\n                    this._state.pastDelay = true;\n                } else {\n                    this._delay = setTimeout(()=>{\n                        this._update({\n                            pastDelay: true\n                        });\n                    }, opts.delay);\n                }\n            }\n            if (typeof opts.timeout === 'number') {\n                this._timeout = setTimeout(()=>{\n                    this._update({\n                        timedOut: true\n                    });\n                }, opts.timeout);\n            }\n        }\n        this._res.promise.then(()=>{\n            this._update({});\n            this._clearTimeouts();\n        }).catch((_err)=>{\n            this._update({});\n            this._clearTimeouts();\n        });\n        this._update({});\n    }\n    _update(partial) {\n        this._state = {\n            ...this._state,\n            error: this._res.error,\n            loaded: this._res.loaded,\n            loading: this._res.loading,\n            ...partial\n        };\n        this._callbacks.forEach((callback)=>callback());\n    }\n    _clearTimeouts() {\n        clearTimeout(this._delay);\n        clearTimeout(this._timeout);\n    }\n    getCurrentValue() {\n        return this._state;\n    }\n    subscribe(callback) {\n        this._callbacks.add(callback);\n        return ()=>{\n            this._callbacks.delete(callback);\n        };\n    }\n    constructor(loadFn, opts){\n        this._loadFn = loadFn;\n        this._opts = opts;\n        this._callbacks = new Set();\n        this._delay = null;\n        this._timeout = null;\n        this.retry();\n    }\n}\nfunction Loadable(opts) {\n    return createLoadableComponent(load, opts);\n}\n_c = Loadable;\nfunction flushInitializers(initializers, ids) {\n    let promises = [];\n    while(initializers.length){\n        let init = initializers.pop();\n        promises.push(init(ids));\n    }\n    return Promise.all(promises).then(()=>{\n        if (initializers.length) {\n            return flushInitializers(initializers, ids);\n        }\n    });\n}\nLoadable.preloadAll = ()=>{\n    return new Promise((resolveInitializers, reject)=>{\n        flushInitializers(ALL_INITIALIZERS).then(resolveInitializers, reject);\n    });\n};\nLoadable.preloadReady = (ids)=>{\n    if (ids === void 0) ids = [];\n    return new Promise((resolvePreload)=>{\n        const res = ()=>{\n            initialized = true;\n            return resolvePreload();\n        };\n        // We always will resolve, errors should be handled within loading UIs.\n        flushInitializers(READY_INITIALIZERS, ids).then(res, res);\n    });\n};\nif (true) {\n    window.__NEXT_PRELOADREADY = Loadable.preloadReady;\n}\nconst _default = Loadable; //# sourceMappingURL=loadable.shared-runtime.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuNV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sb2FkYWJsZS5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxrQ0FBa0M7QUFDbEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFtQkEsR0FDQSx5RUFBeUU7QUFDekUscURBQXFEOzs7OzsyQ0F1UnJEOzs7ZUFBQTs7Ozs0RUFyUmtCOzBEQUNjO0FBRWhDLFNBQVNBLFFBQVFDLEdBQVE7SUFDdkIsT0FBT0EsT0FBT0EsSUFBSUMsT0FBTyxHQUFHRCxJQUFJQyxPQUFPLEdBQUdEO0FBQzVDO0FBRUEsTUFBTUUsbUJBQTBCLEVBQUU7QUFDbEMsTUFBTUMscUJBQTRCLEVBQUU7QUFDcEMsSUFBSUMsY0FBYztBQUVsQixTQUFTQyxLQUFLQyxNQUFXO0lBQ3ZCLElBQUlDLFVBQVVEO0lBRWQsSUFBSUUsUUFBYTtRQUNmQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUFILE1BQU1ELE9BQU8sR0FBR0EsUUFDYkssSUFBSSxDQUFDLENBQUNGO1FBQ0xGLE1BQU1DLE9BQU8sR0FBRztRQUNoQkQsTUFBTUUsTUFBTSxHQUFHQTtRQUNmLE9BQU9BO0lBQ1QsR0FDQ0csS0FBSyxDQUFDLENBQUNDO1FBQ05OLE1BQU1DLE9BQU8sR0FBRztRQUNoQkQsTUFBTUcsS0FBSyxHQUFHRztRQUNkLE1BQU1BO0lBQ1I7SUFFRixPQUFPTjtBQUNUO0FBRUEsU0FBU08sd0JBQXdCQyxNQUFXLEVBQUVDLE9BQVk7O0lBQ3hELElBQUlDLE9BQU9DLE9BQU9DLE1BQU0sQ0FDdEI7UUFDRWQsUUFBUTtRQUNSRyxTQUFTO1FBQ1RZLE9BQU87UUFDUEMsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLFNBQVM7SUFDWCxHQUNBUDtJQUdGLCtCQUErQixHQUMvQixJQUFJUSxlQUFvQjtJQUN4QixTQUFTQztRQUNQLElBQUksQ0FBQ0QsY0FBYztZQUNqQixtRUFBbUU7WUFDbkUsTUFBTUUsTUFBTSxJQUFJQyxxQkFBcUJaLFFBQVFFO1lBQzdDTyxlQUFlO2dCQUNiSSxpQkFBaUJGLElBQUlFLGVBQWUsQ0FBQ0MsSUFBSSxDQUFDSDtnQkFDMUNJLFdBQVdKLElBQUlJLFNBQVMsQ0FBQ0QsSUFBSSxDQUFDSDtnQkFDOUJLLE9BQU9MLElBQUlLLEtBQUssQ0FBQ0YsSUFBSSxDQUFDSDtnQkFDdEJwQixTQUFTb0IsSUFBSXBCLE9BQU8sQ0FBQ3VCLElBQUksQ0FBQ0g7WUFDNUI7UUFDRjtRQUNBLE9BQU9GLGFBQWFsQixPQUFPO0lBQzdCO0lBRUEsY0FBYztJQUNkLElBQUksS0FBNkIsRUFBRTtBQUFBLEVBRWxDO0lBRUQsY0FBYztJQUNkLElBQUksQ0FBQ0gsZUFBZSxPQUFPNkIsTUFBVyxhQUFhO1FBQ2pELDhGQUE4RjtRQUM5RixNQUFNRSxZQUNKakIsS0FBS0ssT0FBTyxJQUFJLFVBQW1DLEtBQUssYUFDcERMLEtBQUtLLE9BQU8sS0FDWkwsS0FBS00sT0FBTztRQUNsQixJQUFJVyxXQUFXO1lBQ2JoQyxtQkFBbUIrQixJQUFJLENBQUMsQ0FBQ0k7Z0JBQ3ZCLEtBQUssTUFBTUMsWUFBWUosVUFBVztvQkFDaEMsSUFBSUcsSUFBSUUsUUFBUSxDQUFDRCxXQUFXO3dCQUMxQixPQUFPYjtvQkFDVDtnQkFDRjtZQUNGO1FBQ0Y7SUFDRjtJQUVBOztRQUNFQTtRQUVBLE1BQU1nQixVQUFVQyxPQUFBQSxPQUFLLENBQUNDLFVBQVUsQ0FBQ0MsOEJBQUFBLGVBQWU7UUFDaEQsSUFBSUgsV0FBV0ksTUFBTUMsT0FBTyxDQUFDN0IsS0FBS00sT0FBTyxHQUFHO1lBQzFDTixLQUFLTSxPQUFPLENBQUN3QixPQUFPLENBQUMsQ0FBQ0M7Z0JBQ3BCUCxRQUFRTztZQUNWO1FBQ0Y7SUFDRjtPQVRTUjtJQVdULDJCQUEyQlUsS0FBVSxFQUFFQyxHQUFROzs7UUFHN0MsTUFBTTVDLFFBQVNtQyxPQUFBQSxPQUFLLENBQVNVLG9CQUFvQixDQUMvQzVCLGFBQWFNLFNBQVMsRUFDdEJOLGFBQWFJLGVBQWUsRUFDNUJKLGFBQWFJLGVBQWU7UUFHOUJjLE9BQUFBLE9BQUssQ0FBQ1csbUJBQW1CLENBQ3ZCRjs2RUFDQSxJQUFPO29CQUNMcEIsT0FBT1AsYUFBYU8sS0FBSztnQkFDM0I7NEVBQ0EsRUFBRTtRQUdKLE9BQU9XLE9BQUFBLE9BQUssQ0FBQ1ksT0FBTztpRUFBQztnQkFDbkIsSUFBSS9DLE1BQU1DLE9BQU8sSUFBSUQsTUFBTUcsS0FBSyxFQUFFO29CQUNoQyxxQkFBT2dDLE9BQUFBLE9BQUssQ0FBQ2EsYUFBYSxDQUFDdEMsS0FBS1QsT0FBTyxFQUFFO3dCQUN2Q2dELFdBQVdqRCxNQUFNQyxPQUFPO3dCQUN4QmlELFdBQVdsRCxNQUFNa0QsU0FBUzt3QkFDMUJDLFVBQVVuRCxNQUFNbUQsUUFBUTt3QkFDeEJoRCxPQUFPSCxNQUFNRyxLQUFLO3dCQUNsQnFCLE9BQU9QLGFBQWFPLEtBQUs7b0JBQzNCO2dCQUNGLE9BQU8sSUFBSXhCLE1BQU1FLE1BQU0sRUFBRTtvQkFDdkIscUJBQU9pQyxPQUFBQSxPQUFLLENBQUNhLGFBQWEsQ0FBQ3pELFFBQVFTLE1BQU1FLE1BQU0sR0FBR3lDO2dCQUNwRCxPQUFPO29CQUNMLE9BQU87Z0JBQ1Q7WUFDRjtnRUFBRztZQUFDQTtZQUFPM0M7U0FBTTtJQUNuQjtRQWhDUzBDOztZQUNQVDs7O0lBaUNGUyxrQkFBa0JVLE9BQU8sR0FBRyxJQUFNbEM7SUFDbEN3QixrQkFBa0JXLFdBQVcsR0FBRztJQUVoQyxxQkFBT2xCLE9BQUFBLE9BQUssQ0FBQ21CLFVBQVUsQ0FBQ1o7QUFDMUI7QUFFQSxNQUFNdEI7SUFrQkpyQixVQUFVO1FBQ1IsT0FBTyxJQUFJLENBQUN3RCxJQUFJLENBQUN4RCxPQUFPO0lBQzFCO0lBRUF5QixRQUFRO1FBQ04sSUFBSSxDQUFDZ0MsY0FBYztRQUNuQixJQUFJLENBQUNELElBQUksR0FBRyxJQUFJLENBQUNFLE9BQU8sQ0FBQyxJQUFJLENBQUNDLEtBQUssQ0FBQzVELE1BQU07UUFFMUMsSUFBSSxDQUFDNkQsTUFBTSxHQUFHO1lBQ1pULFdBQVc7WUFDWEMsVUFBVTtRQUNaO1FBRUEsTUFBTSxFQUFFSSxNQUFNSyxHQUFHLEVBQUVGLE9BQU9oRCxJQUFJLEVBQUUsR0FBRyxJQUFJO1FBRXZDLElBQUlrRCxJQUFJM0QsT0FBTyxFQUFFO1lBQ2YsSUFBSSxPQUFPUyxLQUFLRyxLQUFLLEtBQUssVUFBVTtnQkFDbEMsSUFBSUgsS0FBS0csS0FBSyxLQUFLLEdBQUc7b0JBQ3BCLElBQUksQ0FBQzhDLE1BQU0sQ0FBQ1QsU0FBUyxHQUFHO2dCQUMxQixPQUFPO29CQUNMLElBQUksQ0FBQ1csTUFBTSxHQUFHQyxXQUFXO3dCQUN2QixJQUFJLENBQUNDLE9BQU8sQ0FBQzs0QkFDWGIsV0FBVzt3QkFDYjtvQkFDRixHQUFHeEMsS0FBS0csS0FBSztnQkFDZjtZQUNGO1lBRUEsSUFBSSxPQUFPSCxLQUFLSSxPQUFPLEtBQUssVUFBVTtnQkFDcEMsSUFBSSxDQUFDa0QsUUFBUSxHQUFHRixXQUFXO29CQUN6QixJQUFJLENBQUNDLE9BQU8sQ0FBQzt3QkFBRVosVUFBVTtvQkFBSztnQkFDaEMsR0FBR3pDLEtBQUtJLE9BQU87WUFDakI7UUFDRjtRQUVBLElBQUksQ0FBQ3lDLElBQUksQ0FBQ3hELE9BQU8sQ0FDZEssSUFBSSxDQUFDO1lBQ0osSUFBSSxDQUFDMkQsT0FBTyxDQUFDLENBQUM7WUFDZCxJQUFJLENBQUNQLGNBQWM7UUFDckIsR0FDQ25ELEtBQUssQ0FBQyxDQUFDNEQ7WUFDTixJQUFJLENBQUNGLE9BQU8sQ0FBQyxDQUFDO1lBQ2QsSUFBSSxDQUFDUCxjQUFjO1FBQ3JCO1FBQ0YsSUFBSSxDQUFDTyxPQUFPLENBQUMsQ0FBQztJQUNoQjtJQUVBQSxRQUFRRyxPQUFZLEVBQUU7UUFDcEIsSUFBSSxDQUFDUCxNQUFNLEdBQUc7WUFDWixHQUFHLElBQUksQ0FBQ0EsTUFBTTtZQUNkeEQsT0FBTyxJQUFJLENBQUNvRCxJQUFJLENBQUNwRCxLQUFLO1lBQ3RCRCxRQUFRLElBQUksQ0FBQ3FELElBQUksQ0FBQ3JELE1BQU07WUFDeEJELFNBQVMsSUFBSSxDQUFDc0QsSUFBSSxDQUFDdEQsT0FBTztZQUMxQixHQUFHaUUsT0FBTztRQUNaO1FBQ0EsSUFBSSxDQUFDQyxVQUFVLENBQUMzQixPQUFPLENBQUMsQ0FBQzRCLFdBQWtCQTtJQUM3QztJQUVBWixpQkFBaUI7UUFDZmEsYUFBYSxJQUFJLENBQUNSLE1BQU07UUFDeEJRLGFBQWEsSUFBSSxDQUFDTCxRQUFRO0lBQzVCO0lBRUEzQyxrQkFBa0I7UUFDaEIsT0FBTyxJQUFJLENBQUNzQyxNQUFNO0lBQ3BCO0lBRUFwQyxVQUFVNkMsUUFBYSxFQUFFO1FBQ3ZCLElBQUksQ0FBQ0QsVUFBVSxDQUFDRyxHQUFHLENBQUNGO1FBQ3BCLE9BQU87WUFDTCxJQUFJLENBQUNELFVBQVUsQ0FBQ0ksTUFBTSxDQUFDSDtRQUN6QjtJQUNGO0lBbEZBSSxZQUFZaEUsTUFBVyxFQUFFRSxJQUFTLENBQUU7UUFDbEMsSUFBSSxDQUFDK0MsT0FBTyxHQUFHakQ7UUFDZixJQUFJLENBQUNrRCxLQUFLLEdBQUdoRDtRQUNiLElBQUksQ0FBQ3lELFVBQVUsR0FBRyxJQUFJTTtRQUN0QixJQUFJLENBQUNaLE1BQU0sR0FBRztRQUNkLElBQUksQ0FBQ0csUUFBUSxHQUFHO1FBRWhCLElBQUksQ0FBQ3hDLEtBQUs7SUFDWjtBQTJFRjtBQUVBLGtCQUFrQmQsSUFBUztJQUN6QixPQUFPSCx3QkFBd0JWLE1BQU1hO0FBQ3ZDO0tBRlNnRTtBQUlULFNBQVNDLGtCQUFrQkMsWUFBaUIsRUFBRTlDLEdBQVM7SUFDckQsSUFBSStDLFdBQVcsRUFBRTtJQUVqQixNQUFPRCxhQUFhRSxNQUFNLENBQUU7UUFDMUIsSUFBSTVELE9BQU8wRCxhQUFhRyxHQUFHO1FBQzNCRixTQUFTbkQsSUFBSSxDQUFDUixLQUFLWTtJQUNyQjtJQUVBLE9BQU9rRCxRQUFRQyxHQUFHLENBQUNKLFVBQVV6RSxJQUFJLENBQUM7UUFDaEMsSUFBSXdFLGFBQWFFLE1BQU0sRUFBRTtZQUN2QixPQUFPSCxrQkFBa0JDLGNBQWM5QztRQUN6QztJQUNGO0FBQ0Y7QUFFQTRDLFNBQVNRLFVBQVUsR0FBRztJQUNwQixPQUFPLElBQUlGLFFBQVEsQ0FBQ0cscUJBQXFCQztRQUN2Q1Qsa0JBQWtCakYsa0JBQWtCVSxJQUFJLENBQUMrRSxxQkFBcUJDO0lBQ2hFO0FBQ0Y7QUFFQVYsU0FBU1csWUFBWSxHQUFHLENBQUN2RDtRQUFBQSxRQUFBQSxLQUFBQSxHQUFBQSxNQUEyQixFQUFFO0lBQ3BELE9BQU8sSUFBSWtELFFBQWMsQ0FBQ007UUFDeEIsTUFBTTFCLE1BQU07WUFDVmhFLGNBQWM7WUFDZCxPQUFPMEY7UUFDVDtRQUNBLHVFQUF1RTtRQUN2RVgsa0JBQWtCaEYsb0JBQW9CbUMsS0FBSzFCLElBQUksQ0FBQ3dELEtBQUtBO0lBQ3ZEO0FBQ0Y7QUFRQSxJQUFJLElBQTZCLEVBQUU7SUFDakNuQyxPQUFPOEQsbUJBQW1CLEdBQUdiLFNBQVNXLFlBQVk7QUFDcEQ7TUFFQSxXQUFlWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcRnVsbCBTdGFjayBEZXYgUHJvamVjdHNcXFBvc3RzIFByb2plY3RcXHNyY1xcc2hhcmVkXFxsaWJcXGxvYWRhYmxlLnNoYXJlZC1ydW50aW1lLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUT0RPOiBSZW1vdmUgdXNlIG9mIGBhbnlgIHR5cGUuXG4vKipcbkBjb3B5cmlnaHQgKGMpIDIwMTctcHJlc2VudCBKYW1lcyBLeWxlIDxtZUB0aGVqYW1lc2t5bGUuY29tPlxuIE1JVCBMaWNlbnNlXG4gUGVybWlzc2lvbiBpcyBoZXJlYnkgZ3JhbnRlZCwgZnJlZSBvZiBjaGFyZ2UsIHRvIGFueSBwZXJzb24gb2J0YWluaW5nXG5hIGNvcHkgb2YgdGhpcyBzb2Z0d2FyZSBhbmQgYXNzb2NpYXRlZCBkb2N1bWVudGF0aW9uIGZpbGVzICh0aGVcblwiU29mdHdhcmVcIiksIHRvIGRlYWwgaW4gdGhlIFNvZnR3YXJlIHdpdGhvdXQgcmVzdHJpY3Rpb24sIGluY2x1ZGluZ1xud2l0aG91dCBsaW1pdGF0aW9uIHRoZSByaWdodHMgdG8gdXNlLCBjb3B5LCBtb2RpZnksIG1lcmdlLCBwdWJsaXNoLFxuZGlzdHJpYnV0ZSwgc3VibGljZW5zZSwgYW5kL29yIHNlbGwgY29waWVzIG9mIHRoZSBTb2Z0d2FyZSwgYW5kIHRvXG5wZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpcyBmdXJuaXNoZWQgdG8gZG8gc28sIHN1YmplY3QgdG9cbnRoZSBmb2xsb3dpbmcgY29uZGl0aW9uczpcbiBUaGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSBhbmQgdGhpcyBwZXJtaXNzaW9uIG5vdGljZSBzaGFsbCBiZVxuaW5jbHVkZWQgaW4gYWxsIGNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuXG4gVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiwgV0lUSE9VVCBXQVJSQU5UWSBPRiBBTlkgS0lORCxcbkVYUFJFU1MgT1IgSU1QTElFRCwgSU5DTFVESU5HIEJVVCBOT1QgTElNSVRFRCBUTyBUSEUgV0FSUkFOVElFUyBPRlxuTUVSQ0hBTlRBQklMSVRZLCBGSVRORVNTIEZPUiBBIFBBUlRJQ1VMQVIgUFVSUE9TRSBBTkRcbk5PTklORlJJTkdFTUVOVC4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUlMgT1IgQ09QWVJJR0hUIEhPTERFUlMgQkVcbkxJQUJMRSBGT1IgQU5ZIENMQUlNLCBEQU1BR0VTIE9SIE9USEVSIExJQUJJTElUWSwgV0hFVEhFUiBJTiBBTiBBQ1RJT05cbk9GIENPTlRSQUNULCBUT1JUIE9SIE9USEVSV0lTRSwgQVJJU0lORyBGUk9NLCBPVVQgT0YgT1IgSU4gQ09OTkVDVElPTlxuV0lUSCBUSEUgU09GVFdBUkUgT1IgVEhFIFVTRSBPUiBPVEhFUiBERUFMSU5HUyBJTiBUSEUgU09GVFdBUkVcbiovXG4vLyBodHRwczovL2dpdGh1Yi5jb20vamFtaWVidWlsZHMvcmVhY3QtbG9hZGFibGUvYmxvYi92NS41LjAvc3JjL2luZGV4LmpzXG4vLyBNb2RpZmllZCB0byBiZSBjb21wYXRpYmxlIHdpdGggd2VicGFjayA0IC8gTmV4dC5qc1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBMb2FkYWJsZUNvbnRleHQgfSBmcm9tICcuL2xvYWRhYmxlLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUnXG5cbmZ1bmN0aW9uIHJlc29sdmUob2JqOiBhbnkpIHtcbiAgcmV0dXJuIG9iaiAmJiBvYmouZGVmYXVsdCA/IG9iai5kZWZhdWx0IDogb2JqXG59XG5cbmNvbnN0IEFMTF9JTklUSUFMSVpFUlM6IGFueVtdID0gW11cbmNvbnN0IFJFQURZX0lOSVRJQUxJWkVSUzogYW55W10gPSBbXVxubGV0IGluaXRpYWxpemVkID0gZmFsc2VcblxuZnVuY3Rpb24gbG9hZChsb2FkZXI6IGFueSkge1xuICBsZXQgcHJvbWlzZSA9IGxvYWRlcigpXG5cbiAgbGV0IHN0YXRlOiBhbnkgPSB7XG4gICAgbG9hZGluZzogdHJ1ZSxcbiAgICBsb2FkZWQ6IG51bGwsXG4gICAgZXJyb3I6IG51bGwsXG4gIH1cblxuICBzdGF0ZS5wcm9taXNlID0gcHJvbWlzZVxuICAgIC50aGVuKChsb2FkZWQ6IGFueSkgPT4ge1xuICAgICAgc3RhdGUubG9hZGluZyA9IGZhbHNlXG4gICAgICBzdGF0ZS5sb2FkZWQgPSBsb2FkZWRcbiAgICAgIHJldHVybiBsb2FkZWRcbiAgICB9KVxuICAgIC5jYXRjaCgoZXJyOiBhbnkpID0+IHtcbiAgICAgIHN0YXRlLmxvYWRpbmcgPSBmYWxzZVxuICAgICAgc3RhdGUuZXJyb3IgPSBlcnJcbiAgICAgIHRocm93IGVyclxuICAgIH0pXG5cbiAgcmV0dXJuIHN0YXRlXG59XG5cbmZ1bmN0aW9uIGNyZWF0ZUxvYWRhYmxlQ29tcG9uZW50KGxvYWRGbjogYW55LCBvcHRpb25zOiBhbnkpIHtcbiAgbGV0IG9wdHMgPSBPYmplY3QuYXNzaWduKFxuICAgIHtcbiAgICAgIGxvYWRlcjogbnVsbCxcbiAgICAgIGxvYWRpbmc6IG51bGwsXG4gICAgICBkZWxheTogMjAwLFxuICAgICAgdGltZW91dDogbnVsbCxcbiAgICAgIHdlYnBhY2s6IG51bGwsXG4gICAgICBtb2R1bGVzOiBudWxsLFxuICAgIH0sXG4gICAgb3B0aW9uc1xuICApXG5cbiAgLyoqIEB0eXBlIExvYWRhYmxlU3Vic2NyaXB0aW9uICovXG4gIGxldCBzdWJzY3JpcHRpb246IGFueSA9IG51bGxcbiAgZnVuY3Rpb24gaW5pdCgpIHtcbiAgICBpZiAoIXN1YnNjcmlwdGlvbikge1xuICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11c2UtYmVmb3JlLWRlZmluZVxuICAgICAgY29uc3Qgc3ViID0gbmV3IExvYWRhYmxlU3Vic2NyaXB0aW9uKGxvYWRGbiwgb3B0cylcbiAgICAgIHN1YnNjcmlwdGlvbiA9IHtcbiAgICAgICAgZ2V0Q3VycmVudFZhbHVlOiBzdWIuZ2V0Q3VycmVudFZhbHVlLmJpbmQoc3ViKSxcbiAgICAgICAgc3Vic2NyaWJlOiBzdWIuc3Vic2NyaWJlLmJpbmQoc3ViKSxcbiAgICAgICAgcmV0cnk6IHN1Yi5yZXRyeS5iaW5kKHN1YiksXG4gICAgICAgIHByb21pc2U6IHN1Yi5wcm9taXNlLmJpbmQoc3ViKSxcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHN1YnNjcmlwdGlvbi5wcm9taXNlKClcbiAgfVxuXG4gIC8vIFNlcnZlciBvbmx5XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xuICAgIEFMTF9JTklUSUFMSVpFUlMucHVzaChpbml0KVxuICB9XG5cbiAgLy8gQ2xpZW50IG9ubHlcbiAgaWYgKCFpbml0aWFsaXplZCAmJiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIC8vIHJlcXVpcmUucmVzb2x2ZVdlYWsgY2hlY2sgaXMgbmVlZGVkIGZvciBlbnZpcm9ubWVudHMgdGhhdCBkb24ndCBoYXZlIGl0IGF2YWlsYWJsZSBsaWtlIEplc3RcbiAgICBjb25zdCBtb2R1bGVJZHMgPVxuICAgICAgb3B0cy53ZWJwYWNrICYmIHR5cGVvZiAocmVxdWlyZSBhcyBhbnkpLnJlc29sdmVXZWFrID09PSAnZnVuY3Rpb24nXG4gICAgICAgID8gb3B0cy53ZWJwYWNrKClcbiAgICAgICAgOiBvcHRzLm1vZHVsZXNcbiAgICBpZiAobW9kdWxlSWRzKSB7XG4gICAgICBSRUFEWV9JTklUSUFMSVpFUlMucHVzaCgoaWRzOiBhbnkpID0+IHtcbiAgICAgICAgZm9yIChjb25zdCBtb2R1bGVJZCBvZiBtb2R1bGVJZHMpIHtcbiAgICAgICAgICBpZiAoaWRzLmluY2x1ZGVzKG1vZHVsZUlkKSkge1xuICAgICAgICAgICAgcmV0dXJuIGluaXQoKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiB1c2VMb2FkYWJsZU1vZHVsZSgpIHtcbiAgICBpbml0KClcblxuICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KExvYWRhYmxlQ29udGV4dClcbiAgICBpZiAoY29udGV4dCAmJiBBcnJheS5pc0FycmF5KG9wdHMubW9kdWxlcykpIHtcbiAgICAgIG9wdHMubW9kdWxlcy5mb3JFYWNoKChtb2R1bGVOYW1lOiBhbnkpID0+IHtcbiAgICAgICAgY29udGV4dChtb2R1bGVOYW1lKVxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiBMb2FkYWJsZUNvbXBvbmVudChwcm9wczogYW55LCByZWY6IGFueSkge1xuICAgIHVzZUxvYWRhYmxlTW9kdWxlKClcblxuICAgIGNvbnN0IHN0YXRlID0gKFJlYWN0IGFzIGFueSkudXNlU3luY0V4dGVybmFsU3RvcmUoXG4gICAgICBzdWJzY3JpcHRpb24uc3Vic2NyaWJlLFxuICAgICAgc3Vic2NyaXB0aW9uLmdldEN1cnJlbnRWYWx1ZSxcbiAgICAgIHN1YnNjcmlwdGlvbi5nZXRDdXJyZW50VmFsdWVcbiAgICApXG5cbiAgICBSZWFjdC51c2VJbXBlcmF0aXZlSGFuZGxlKFxuICAgICAgcmVmLFxuICAgICAgKCkgPT4gKHtcbiAgICAgICAgcmV0cnk6IHN1YnNjcmlwdGlvbi5yZXRyeSxcbiAgICAgIH0pLFxuICAgICAgW11cbiAgICApXG5cbiAgICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgICBpZiAoc3RhdGUubG9hZGluZyB8fCBzdGF0ZS5lcnJvcikge1xuICAgICAgICByZXR1cm4gUmVhY3QuY3JlYXRlRWxlbWVudChvcHRzLmxvYWRpbmcsIHtcbiAgICAgICAgICBpc0xvYWRpbmc6IHN0YXRlLmxvYWRpbmcsXG4gICAgICAgICAgcGFzdERlbGF5OiBzdGF0ZS5wYXN0RGVsYXksXG4gICAgICAgICAgdGltZWRPdXQ6IHN0YXRlLnRpbWVkT3V0LFxuICAgICAgICAgIGVycm9yOiBzdGF0ZS5lcnJvcixcbiAgICAgICAgICByZXRyeTogc3Vic2NyaXB0aW9uLnJldHJ5LFxuICAgICAgICB9KVxuICAgICAgfSBlbHNlIGlmIChzdGF0ZS5sb2FkZWQpIHtcbiAgICAgICAgcmV0dXJuIFJlYWN0LmNyZWF0ZUVsZW1lbnQocmVzb2x2ZShzdGF0ZS5sb2FkZWQpLCBwcm9wcylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBudWxsXG4gICAgICB9XG4gICAgfSwgW3Byb3BzLCBzdGF0ZV0pXG4gIH1cblxuICBMb2FkYWJsZUNvbXBvbmVudC5wcmVsb2FkID0gKCkgPT4gaW5pdCgpXG4gIExvYWRhYmxlQ29tcG9uZW50LmRpc3BsYXlOYW1lID0gJ0xvYWRhYmxlQ29tcG9uZW50J1xuXG4gIHJldHVybiBSZWFjdC5mb3J3YXJkUmVmKExvYWRhYmxlQ29tcG9uZW50KVxufVxuXG5jbGFzcyBMb2FkYWJsZVN1YnNjcmlwdGlvbiB7XG4gIF9sb2FkRm46IGFueVxuICBfb3B0czogYW55XG4gIF9jYWxsYmFja3M6IGFueVxuICBfZGVsYXk6IGFueVxuICBfdGltZW91dDogYW55XG4gIF9yZXM6IGFueVxuICBfc3RhdGU6IGFueVxuICBjb25zdHJ1Y3Rvcihsb2FkRm46IGFueSwgb3B0czogYW55KSB7XG4gICAgdGhpcy5fbG9hZEZuID0gbG9hZEZuXG4gICAgdGhpcy5fb3B0cyA9IG9wdHNcbiAgICB0aGlzLl9jYWxsYmFja3MgPSBuZXcgU2V0KClcbiAgICB0aGlzLl9kZWxheSA9IG51bGxcbiAgICB0aGlzLl90aW1lb3V0ID0gbnVsbFxuXG4gICAgdGhpcy5yZXRyeSgpXG4gIH1cblxuICBwcm9taXNlKCkge1xuICAgIHJldHVybiB0aGlzLl9yZXMucHJvbWlzZVxuICB9XG5cbiAgcmV0cnkoKSB7XG4gICAgdGhpcy5fY2xlYXJUaW1lb3V0cygpXG4gICAgdGhpcy5fcmVzID0gdGhpcy5fbG9hZEZuKHRoaXMuX29wdHMubG9hZGVyKVxuXG4gICAgdGhpcy5fc3RhdGUgPSB7XG4gICAgICBwYXN0RGVsYXk6IGZhbHNlLFxuICAgICAgdGltZWRPdXQ6IGZhbHNlLFxuICAgIH1cblxuICAgIGNvbnN0IHsgX3JlczogcmVzLCBfb3B0czogb3B0cyB9ID0gdGhpc1xuXG4gICAgaWYgKHJlcy5sb2FkaW5nKSB7XG4gICAgICBpZiAodHlwZW9mIG9wdHMuZGVsYXkgPT09ICdudW1iZXInKSB7XG4gICAgICAgIGlmIChvcHRzLmRlbGF5ID09PSAwKSB7XG4gICAgICAgICAgdGhpcy5fc3RhdGUucGFzdERlbGF5ID0gdHJ1ZVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRoaXMuX2RlbGF5ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLl91cGRhdGUoe1xuICAgICAgICAgICAgICBwYXN0RGVsYXk6IHRydWUsXG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH0sIG9wdHMuZGVsYXkpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiBvcHRzLnRpbWVvdXQgPT09ICdudW1iZXInKSB7XG4gICAgICAgIHRoaXMuX3RpbWVvdXQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICB0aGlzLl91cGRhdGUoeyB0aW1lZE91dDogdHJ1ZSB9KVxuICAgICAgICB9LCBvcHRzLnRpbWVvdXQpXG4gICAgICB9XG4gICAgfVxuXG4gICAgdGhpcy5fcmVzLnByb21pc2VcbiAgICAgIC50aGVuKCgpID0+IHtcbiAgICAgICAgdGhpcy5fdXBkYXRlKHt9KVxuICAgICAgICB0aGlzLl9jbGVhclRpbWVvdXRzKClcbiAgICAgIH0pXG4gICAgICAuY2F0Y2goKF9lcnI6IGFueSkgPT4ge1xuICAgICAgICB0aGlzLl91cGRhdGUoe30pXG4gICAgICAgIHRoaXMuX2NsZWFyVGltZW91dHMoKVxuICAgICAgfSlcbiAgICB0aGlzLl91cGRhdGUoe30pXG4gIH1cblxuICBfdXBkYXRlKHBhcnRpYWw6IGFueSkge1xuICAgIHRoaXMuX3N0YXRlID0ge1xuICAgICAgLi4udGhpcy5fc3RhdGUsXG4gICAgICBlcnJvcjogdGhpcy5fcmVzLmVycm9yLFxuICAgICAgbG9hZGVkOiB0aGlzLl9yZXMubG9hZGVkLFxuICAgICAgbG9hZGluZzogdGhpcy5fcmVzLmxvYWRpbmcsXG4gICAgICAuLi5wYXJ0aWFsLFxuICAgIH1cbiAgICB0aGlzLl9jYWxsYmFja3MuZm9yRWFjaCgoY2FsbGJhY2s6IGFueSkgPT4gY2FsbGJhY2soKSlcbiAgfVxuXG4gIF9jbGVhclRpbWVvdXRzKCkge1xuICAgIGNsZWFyVGltZW91dCh0aGlzLl9kZWxheSlcbiAgICBjbGVhclRpbWVvdXQodGhpcy5fdGltZW91dClcbiAgfVxuXG4gIGdldEN1cnJlbnRWYWx1ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fc3RhdGVcbiAgfVxuXG4gIHN1YnNjcmliZShjYWxsYmFjazogYW55KSB7XG4gICAgdGhpcy5fY2FsbGJhY2tzLmFkZChjYWxsYmFjaylcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdGhpcy5fY2FsbGJhY2tzLmRlbGV0ZShjYWxsYmFjaylcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gTG9hZGFibGUob3B0czogYW55KSB7XG4gIHJldHVybiBjcmVhdGVMb2FkYWJsZUNvbXBvbmVudChsb2FkLCBvcHRzKVxufVxuXG5mdW5jdGlvbiBmbHVzaEluaXRpYWxpemVycyhpbml0aWFsaXplcnM6IGFueSwgaWRzPzogYW55KTogYW55IHtcbiAgbGV0IHByb21pc2VzID0gW11cblxuICB3aGlsZSAoaW5pdGlhbGl6ZXJzLmxlbmd0aCkge1xuICAgIGxldCBpbml0ID0gaW5pdGlhbGl6ZXJzLnBvcCgpXG4gICAgcHJvbWlzZXMucHVzaChpbml0KGlkcykpXG4gIH1cblxuICByZXR1cm4gUHJvbWlzZS5hbGwocHJvbWlzZXMpLnRoZW4oKCkgPT4ge1xuICAgIGlmIChpbml0aWFsaXplcnMubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gZmx1c2hJbml0aWFsaXplcnMoaW5pdGlhbGl6ZXJzLCBpZHMpXG4gICAgfVxuICB9KVxufVxuXG5Mb2FkYWJsZS5wcmVsb2FkQWxsID0gKCkgPT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmVJbml0aWFsaXplcnMsIHJlamVjdCkgPT4ge1xuICAgIGZsdXNoSW5pdGlhbGl6ZXJzKEFMTF9JTklUSUFMSVpFUlMpLnRoZW4ocmVzb2x2ZUluaXRpYWxpemVycywgcmVqZWN0KVxuICB9KVxufVxuXG5Mb2FkYWJsZS5wcmVsb2FkUmVhZHkgPSAoaWRzOiAoc3RyaW5nIHwgbnVtYmVyKVtdID0gW10pOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlPHZvaWQ+KChyZXNvbHZlUHJlbG9hZCkgPT4ge1xuICAgIGNvbnN0IHJlcyA9ICgpID0+IHtcbiAgICAgIGluaXRpYWxpemVkID0gdHJ1ZVxuICAgICAgcmV0dXJuIHJlc29sdmVQcmVsb2FkKClcbiAgICB9XG4gICAgLy8gV2UgYWx3YXlzIHdpbGwgcmVzb2x2ZSwgZXJyb3JzIHNob3VsZCBiZSBoYW5kbGVkIHdpdGhpbiBsb2FkaW5nIFVJcy5cbiAgICBmbHVzaEluaXRpYWxpemVycyhSRUFEWV9JTklUSUFMSVpFUlMsIGlkcykudGhlbihyZXMsIHJlcylcbiAgfSlcbn1cblxuZGVjbGFyZSBnbG9iYWwge1xuICBpbnRlcmZhY2UgV2luZG93IHtcbiAgICBfX05FWFRfUFJFTE9BRFJFQURZPzogKGlkcz86IChzdHJpbmcgfCBudW1iZXIpW10pID0+IFByb21pc2U8dm9pZD5cbiAgfVxufVxuXG5pZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgd2luZG93Ll9fTkVYVF9QUkVMT0FEUkVBRFkgPSBMb2FkYWJsZS5wcmVsb2FkUmVhZHlcbn1cblxuZXhwb3J0IGRlZmF1bHQgTG9hZGFibGVcbiJdLCJuYW1lcyI6WyJyZXNvbHZlIiwib2JqIiwiZGVmYXVsdCIsIkFMTF9JTklUSUFMSVpFUlMiLCJSRUFEWV9JTklUSUFMSVpFUlMiLCJpbml0aWFsaXplZCIsImxvYWQiLCJsb2FkZXIiLCJwcm9taXNlIiwic3RhdGUiLCJsb2FkaW5nIiwibG9hZGVkIiwiZXJyb3IiLCJ0aGVuIiwiY2F0Y2giLCJlcnIiLCJjcmVhdGVMb2FkYWJsZUNvbXBvbmVudCIsImxvYWRGbiIsIm9wdGlvbnMiLCJvcHRzIiwiT2JqZWN0IiwiYXNzaWduIiwiZGVsYXkiLCJ0aW1lb3V0Iiwid2VicGFjayIsIm1vZHVsZXMiLCJzdWJzY3JpcHRpb24iLCJpbml0Iiwic3ViIiwiTG9hZGFibGVTdWJzY3JpcHRpb24iLCJnZXRDdXJyZW50VmFsdWUiLCJiaW5kIiwic3Vic2NyaWJlIiwicmV0cnkiLCJ3aW5kb3ciLCJwdXNoIiwibW9kdWxlSWRzIiwicmVxdWlyZSIsInJlc29sdmVXZWFrIiwiaWRzIiwibW9kdWxlSWQiLCJpbmNsdWRlcyIsInVzZUxvYWRhYmxlTW9kdWxlIiwiY29udGV4dCIsIlJlYWN0IiwidXNlQ29udGV4dCIsIkxvYWRhYmxlQ29udGV4dCIsIkFycmF5IiwiaXNBcnJheSIsImZvckVhY2giLCJtb2R1bGVOYW1lIiwiTG9hZGFibGVDb21wb25lbnQiLCJwcm9wcyIsInJlZiIsInVzZVN5bmNFeHRlcm5hbFN0b3JlIiwidXNlSW1wZXJhdGl2ZUhhbmRsZSIsInVzZU1lbW8iLCJjcmVhdGVFbGVtZW50IiwiaXNMb2FkaW5nIiwicGFzdERlbGF5IiwidGltZWRPdXQiLCJwcmVsb2FkIiwiZGlzcGxheU5hbWUiLCJmb3J3YXJkUmVmIiwiX3JlcyIsIl9jbGVhclRpbWVvdXRzIiwiX2xvYWRGbiIsIl9vcHRzIiwiX3N0YXRlIiwicmVzIiwiX2RlbGF5Iiwic2V0VGltZW91dCIsIl91cGRhdGUiLCJfdGltZW91dCIsIl9lcnIiLCJwYXJ0aWFsIiwiX2NhbGxiYWNrcyIsImNhbGxiYWNrIiwiY2xlYXJUaW1lb3V0IiwiYWRkIiwiZGVsZXRlIiwiY29uc3RydWN0b3IiLCJTZXQiLCJMb2FkYWJsZSIsImZsdXNoSW5pdGlhbGl6ZXJzIiwiaW5pdGlhbGl6ZXJzIiwicHJvbWlzZXMiLCJsZW5ndGgiLCJwb3AiLCJQcm9taXNlIiwiYWxsIiwicHJlbG9hZEFsbCIsInJlc29sdmVJbml0aWFsaXplcnMiLCJyZWplY3QiLCJwcmVsb2FkUmVhZHkiLCJyZXNvbHZlUHJlbG9hZCIsIl9fTkVYVF9QUkVMT0FEUkVBRFkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/loadable.shared-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js ***!
  \*****************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/dynamic */ \"(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/dynamic.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuNV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2R5bmFtaWMuanMiLCJtYXBwaW5ncyI6IkFBQUEsc05BQXFEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxGdWxsIFN0YWNrIERldiBQcm9qZWN0c1xcUG9zdHMgUHJvamVjdFxcTUZLQiBUYXNrIEJvYXJkXFxGcm9udGVuZCBLbm93bGVkZ2UgQmFzZSBBcHBcXHNoZWxsLWFwcFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkeW5hbWljLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L3NoYXJlZC9saWIvZHluYW1pYycpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShellHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/FederatedComponent */ \"(pages-dir-browser)/./components/FederatedComponent.tsx\");\n\n\nfunction ShellHomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Shell App\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"This is the shell application.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    scope: \"authApp\",\n                    module: \"./Login\",\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Loading Auth...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 23\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Notes App\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        scope: \"notesApp\",\n                        module: \"./NotesEditor\",\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Loading Notes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 21\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n_c = ShellHomePage;\nvar _c;\n$RefreshReg$(_c, \"ShellHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrRTtBQUVuRCxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7OzBCQUNDLDhEQUFDQzswQkFBRzs7Ozs7OzBCQUNKLDhEQUFDQzswQkFBRTs7Ozs7OzBCQUNILDhEQUFDRjswQkFDSyw0RUFBQ0Ysc0VBQWtCQTtvQkFDbkJLLE9BQU07b0JBQ05DLFFBQU87b0JBQ1BDLHdCQUFVLDhEQUFDTDtrQ0FBSTs7Ozs7Ozs7Ozs7Ozs7OzswQkFHckIsOERBQUNBOztrQ0FDQyw4REFBQ007a0NBQUc7Ozs7OztrQ0FDSiw4REFBQ1Isc0VBQWtCQTt3QkFDakJLLE9BQU07d0JBQ05DLFFBQU87d0JBQ1BDLHdCQUFVLDhEQUFDTDtzQ0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLekI7S0F0QndCRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcRnVsbCBTdGFjayBEZXYgUHJvamVjdHNcXFBvc3RzIFByb2plY3RcXE1GS0IgVGFzayBCb2FyZFxcRnJvbnRlbmQgS25vd2xlZGdlIEJhc2UgQXBwXFxzaGVsbC1hcHBcXHBhZ2VzXFxpbmRleC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEZlZGVyYXRlZENvbXBvbmVudCBmcm9tIFwiLi4vY29tcG9uZW50cy9GZWRlcmF0ZWRDb21wb25lbnRcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNoZWxsSG9tZVBhZ2UoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXY+XHJcbiAgICAgIDxoMT5TaGVsbCBBcHA8L2gxPlxyXG4gICAgICA8cD5UaGlzIGlzIHRoZSBzaGVsbCBhcHBsaWNhdGlvbi48L3A+XHJcbiAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxGZWRlcmF0ZWRDb21wb25lbnRcclxuICAgICAgICAgICAgc2NvcGU9XCJhdXRoQXBwXCJcclxuICAgICAgICAgICAgbW9kdWxlPVwiLi9Mb2dpblwiXHJcbiAgICAgICAgICAgIGZhbGxiYWNrPXs8ZGl2PkxvYWRpbmcgQXV0aC4uLjwvZGl2Pn1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXY+XHJcbiAgICAgICAgPGgyPk5vdGVzIEFwcDwvaDI+XHJcbiAgICAgICAgPEZlZGVyYXRlZENvbXBvbmVudFxyXG4gICAgICAgICAgc2NvcGU9XCJub3Rlc0FwcFwiXHJcbiAgICAgICAgICBtb2R1bGU9XCIuL05vdGVzRWRpdG9yXCJcclxuICAgICAgICAgIGZhbGxiYWNrPXs8ZGl2PkxvYWRpbmcgTm90ZXMuLi48L2Rpdj59XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJGZWRlcmF0ZWRDb21wb25lbnQiLCJTaGVsbEhvbWVQYWdlIiwiZGl2IiwiaDEiLCJwIiwic2NvcGUiLCJtb2R1bGUiLCJmYWxsYmFjayIsImgyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/index.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cbiche%5CFull%20Stack%20Dev%20Projects%5CPosts%20Project%5CMFKB%20Task%20Board%5CFrontend%20Knowledge%20Base%20App%5Cshell-app%5Cpages%5Cindex.tsx&page=%2F!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);