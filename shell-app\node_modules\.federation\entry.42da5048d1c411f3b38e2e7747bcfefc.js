import federation from 'C:/Users/<USER>/Full Stack Dev Projects/Posts Project/MFKB Task Board/Frontend Knowledge Base App/shell-app/node_modules/.pnpm/@module-federation+webpack-bundler-runtime@0.16.0/node_modules/@module-federation/webpack-bundler-runtime/dist/index.esm.js';
import plugin_0 from 'C:/Users/<USER>/Full Stack Dev Projects/Posts Project/MFKB Task Board/Frontend Knowledge Base App/shell-app/node_modules/.pnpm/@module-federation+nextjs-m_410daccdb6b3c8f4035756cfefa9aa92/node_modules/@module-federation/nextjs-mf/dist/src/plugins/container/runtimePlugin.cjs?runtimePlugin';

if(!__webpack_require__.federation.runtime){
	var prevFederation = __webpack_require__.federation;
	__webpack_require__.federation = {}
	for(var key in federation){
		__webpack_require__.federation[key] = federation[key];
	}
	for(var key in prevFederation){
		__webpack_require__.federation[key] = prevFederation[key];
	}
}
if(!__webpack_require__.federation.instance){
	var pluginsToAdd = [
		plugin_0 ? (plugin_0.default || plugin_0)() : false,
	].filter(Boolean);
	__webpack_require__.federation.initOptions.plugins = __webpack_require__.federation.initOptions.plugins ? 
	__webpack_require__.federation.initOptions.plugins.concat(pluginsToAdd) : pluginsToAdd;
	__webpack_require__.federation.instance = __webpack_require__.federation.runtime.init(__webpack_require__.federation.initOptions);
	if(__webpack_require__.federation.attachShareScopeMap){
		__webpack_require__.federation.attachShareScopeMap(__webpack_require__)
	}
	if(__webpack_require__.federation.installInitialConsumes){
		__webpack_require__.federation.installInitialConsumes()
	}

	if(!__webpack_require__.federation.isMFRemote && __webpack_require__.federation.prefetch){
	__webpack_require__.federation.prefetch()
	}
}