"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./components/FederatedComponent.tsx":
/*!*******************************************!*\
  !*** ./components/FederatedComponent.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst FederatedComponent = ({ scope, module, fallback = null })=>{\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"FederatedComponent.useEffect\": ()=>{\n            const checkRemoteAvailability = {\n                \"FederatedComponent.useEffect.checkRemoteAvailability\": ()=>{\n                    if (false) // removed by dead control flow\n{} else {\n                        // Retry after a short delay\n                        setTimeout(checkRemoteAvailability, 100);\n                    }\n                }\n            }[\"FederatedComponent.useEffect.checkRemoteAvailability\"];\n            checkRemoteAvailability();\n        }\n    }[\"FederatedComponent.useEffect\"], [\n        scope\n    ]);\n    if (!isReady) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                \"Error loading \",\n                scope,\n                \"/\",\n                module,\n                \": \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n            lineNumber: 34,\n            columnNumber: 12\n        }, undefined);\n    }\n    const Component = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(// Dynamically load the remote module using scope and module props\n    ()=>window[scope].get(module).then((factory)=>{\n            try {\n                const Module = factory();\n                return Module;\n            } catch (err) {\n                console.error(`Error loading module ${scope}/${module}:`, err);\n                setError(err instanceof Error ? err.message : 'Unknown error');\n                throw err;\n            }\n        }).catch((err)=>{\n            console.error(`Failed to load ${scope}/${module}:`, err);\n            setError(err.message || 'Failed to load module');\n            // Return a fallback component\n            return ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        \"Failed to load \",\n                        scope,\n                        \"/\",\n                        module\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 22\n                }, undefined);\n        }), {\n        loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallback\n            }, void 0, false),\n        ssr: false // Disable SSR for federated components\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n        fallback: fallback,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\components\\\\FederatedComponent.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FederatedComponent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/FederatedComponent.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"(pages-dir-node)/./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\_app.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFZSxTQUFTQSxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzlELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcRnVsbCBTdGFjayBEZXYgUHJvamVjdHNcXFBvc3RzIFByb2plY3RcXE1GS0IgVGFzayBCb2FyZFxcRnJvbnRlbmQgS25vd2xlZGdlIEJhc2UgQXBwXFxzaGVsbC1hcHBcXHBhZ2VzXFxfYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNeUFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+O1xufVxuIl0sIm5hbWVzIjpbIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShellHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/FederatedComponent */ \"(pages-dir-node)/./components/FederatedComponent.tsx\");\n\n\nfunction ShellHomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Shell App\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"This is the shell application.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    scope: \"authApp\",\n                    module: \"./Login\",\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Loading Auth...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 23\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Notes App\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FederatedComponent__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        scope: \"notesApp\",\n                        module: \"./NotesEditor\",\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Loading Notes...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 21\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Full Stack Dev Projects\\\\Posts Project\\\\MFKB Task Board\\\\Frontend Knowledge Base App\\\\shell-app\\\\pages\\\\index.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrRTtBQUVuRCxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7OzBCQUNDLDhEQUFDQzswQkFBRzs7Ozs7OzBCQUNKLDhEQUFDQzswQkFBRTs7Ozs7OzBCQUNILDhEQUFDRjswQkFDSyw0RUFBQ0Ysc0VBQWtCQTtvQkFDbkJLLE9BQU07b0JBQ05DLFFBQU87b0JBQ1BDLHdCQUFVLDhEQUFDTDtrQ0FBSTs7Ozs7Ozs7Ozs7Ozs7OzswQkFHckIsOERBQUNBOztrQ0FDQyw4REFBQ007a0NBQUc7Ozs7OztrQ0FDSiw4REFBQ1Isc0VBQWtCQTt3QkFDakJLLE9BQU07d0JBQ05DLFFBQU87d0JBQ1BDLHdCQUFVLDhEQUFDTDtzQ0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLekIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXEZ1bGwgU3RhY2sgRGV2IFByb2plY3RzXFxQb3N0cyBQcm9qZWN0XFxNRktCIFRhc2sgQm9hcmRcXEZyb250ZW5kIEtub3dsZWRnZSBCYXNlIEFwcFxcc2hlbGwtYXBwXFxwYWdlc1xcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBGZWRlcmF0ZWRDb21wb25lbnQgZnJvbSBcIi4uL2NvbXBvbmVudHMvRmVkZXJhdGVkQ29tcG9uZW50XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaGVsbEhvbWVQYWdlKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2PlxyXG4gICAgICA8aDE+U2hlbGwgQXBwPC9oMT5cclxuICAgICAgPHA+VGhpcyBpcyB0aGUgc2hlbGwgYXBwbGljYXRpb24uPC9wPlxyXG4gICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8RmVkZXJhdGVkQ29tcG9uZW50XHJcbiAgICAgICAgICAgIHNjb3BlPVwiYXV0aEFwcFwiXHJcbiAgICAgICAgICAgIG1vZHVsZT1cIi4vTG9naW5cIlxyXG4gICAgICAgICAgICBmYWxsYmFjaz17PGRpdj5Mb2FkaW5nIEF1dGguLi48L2Rpdj59XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2PlxyXG4gICAgICAgIDxoMj5Ob3RlcyBBcHA8L2gyPlxyXG4gICAgICAgIDxGZWRlcmF0ZWRDb21wb25lbnRcclxuICAgICAgICAgIHNjb3BlPVwibm90ZXNBcHBcIlxyXG4gICAgICAgICAgbW9kdWxlPVwiLi9Ob3Rlc0VkaXRvclwiXHJcbiAgICAgICAgICBmYWxsYmFjaz17PGRpdj5Mb2FkaW5nIE5vdGVzLi4uPC9kaXY+fVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiRmVkZXJhdGVkQ29tcG9uZW50IiwiU2hlbGxIb21lUGFnZSIsImRpdiIsImgxIiwicCIsInNjb3BlIiwibW9kdWxlIiwiZmFsbGJhY2siLCJoMiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/index.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@swc+helpers@0.5.15"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();